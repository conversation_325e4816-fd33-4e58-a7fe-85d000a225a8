package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	_ "github.com/jackc/pgx/v5/stdlib" // 底線 _ 代表我們只需要這個 package 的 side-effects (註冊 driver)
)

// TrainingLog struct 對應到我們的 DB table
// 這是我們自製 ORM 的第一步，把 DB row 轉成 Go 的物件
type TrainingLog struct {
	ID        int64        `json:"id"`
	LiftType  string       `json:"lift_type"`
	WeightKG  float64      `json:"weight_kg"`
	Reps      int          `json:"reps"`
	Sets      int          `json:"sets"`
	LogDate   time.Time    `json:"log_date"`
	DeletedAt sql.NullTime `json:"deleted_at"` // 用 sql.NullTime 來處理可以為 NULL 的時間欄位
}

func main() {

	dbURL := os.Getenv("DATABASE_URL")
	if dbURL == "" {

		dbURL = "postgres://admin:password@localhost:5432/mydb?sslmode=disable"
		log.Println("WARNING: DATABASE_URL not set, using default value")
	}

	db, err := sql.Open("pgx", dbURL)
	if err != nil {
		log.Fatalf("Unable to connect to database: %v\n", err)
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		log.Fatalf("Database ping failed: %v\n", err)
	}

	fmt.Println("Successfully connected to the database! 🐘")

	r := gin.Default()

	r.GET("/logs", func(c *gin.Context) {
		getLogsHandler(c, db)
	})

	r.POST("/logs", func(c *gin.Context) {
		createLogHandler(c, db)
	})

	fmt.Println("Starting server on :8080... 🚀")
	r.Run(":8080")
}

// implement getLogsHandler
func getLogsHandler(c *gin.Context, db *sql.DB) {
	// 用 SELECT query 取出所有資料
	rows, err := db.Query("SELECT * FROM training_logs")
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to fetch logs"})
		return
	}
	defer rows.Close()

	// 把資料一行一行讀出來，裝進 TrainingLog 物件
	var logs []TrainingLog // 用 slice 把多筆資料裝起來
	for rows.Next() {
		var log TrainingLog
		err := rows.Scan(&log.ID, &log.LiftType, &log.WeightKG, &log.Reps, &log.Sets, &log.LogDate, &log.DeletedAt)
		if err != nil {
			c.JSON(500, gin.H{"error": "Failed to scan row"})
			return
		}
		logs = append(logs, log)
	}

	// 把資料回傳給前端
	c.JSON(200, logs)
}

func createLogHandler(c *gin.Context, db *sql.DB) {
	// 用 ShouldBindJSON 把前端送來的 JSON 轉成 TrainingLog 物件
	var log TrainingLog
	if err := c.ShouldBindJSON(&log); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request"})
		return
	}

	// 用 INSERT 把資料存進資料庫，使用 RETURNING 來取得新建立的 ID (PostgreSQL 專用)
	query := `INSERT INTO training_logs (lift_type, weight_kg, reps, sets, log_date)
			  VALUES ($1, $2, $3, $4, $5) 
			  RETURNING id, lift_type, weight_kg, reps, sets, log_date`
	var createdLog TrainingLog

	err := db.QueryRow(query, log.LiftType, log.WeightKG, log.Reps, log.Sets, log.LogDate).Scan(&createdLog.ID,
		&createdLog.LiftType,
		&createdLog.WeightKG,
		&createdLog.Reps,
		&createdLog.Sets,
		&createdLog.LogDate,
	)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to create log"})
		return
	}
	c.JSON(201, createdLog)
}
